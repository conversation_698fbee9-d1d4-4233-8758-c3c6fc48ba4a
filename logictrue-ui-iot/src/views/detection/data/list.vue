<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="解析状态" prop="parseStatus">
        <el-select v-model="queryParams.parseStatus" placeholder="请选择解析状态" clearable>
          <el-option label="待解析" value="0" />
          <el-option label="解析成功" value="1" />
          <el-option label="解析失败" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleUpload"
        >上传数据</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="showStatistics = true"
        >统计信息</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <div class="table-wrapper">
      <div class="table-scroll-tip" v-if="showScrollTip">
        <i class="el-icon-d-arrow-left"></i>
        <span>表格内容较宽，可左右滑动查看</span>
        <i class="el-icon-d-arrow-right"></i>
      </div>
      <div class="table-container" @scroll="handleTableScroll">
        <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="文件名" align="center" prop="fileName" min-width="180" show-overflow-tooltip />
      <el-table-column label="设备编码" align="center" prop="deviceCode" width="110" />
      <el-table-column label="设备名称" align="center" prop="deviceName" width="110" />
      <el-table-column label="模板名称" align="center" prop="templateName" min-width="140" show-overflow-tooltip />
      <el-table-column label="文件大小" align="center" prop="fileSize" width="90">
        <template slot-scope="scope">
          <span>{{ formatFileSize(scope.row.fileSize) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="解析状态" align="center" prop="parseStatus" width="90">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.parseStatus)">
            {{ getStatusText(scope.row.parseStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="基础字段" align="center" prop="basicFieldsCount" width="70" />
      <el-table-column label="表格行数" align="center" prop="tableRowsCount" width="70" />
      <el-table-column label="解析时间" align="center" prop="parseTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.parseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            v-if="scope.row.parseStatus === 2"
            @click="handleReparse(scope.row)"
          >重新解析</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleExport(scope.row)"
          >导出</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <lt-pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 20, 30, 50]"
      :auto="false"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList"
    />

    <!-- 统计信息对话框 -->
    <el-dialog title="解析状态统计" :visible.sync="showStatistics" width="500px">
      <div class="statistics-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总数据量</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number success">{{ statistics.success }}</div>
              <div class="stat-label">解析成功</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number warning">{{ statistics.pending }}</div>
              <div class="stat-label">待解析</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number danger">{{ statistics.failed }}</div>
              <div class="stat-label">解析失败</div>
            </div>
          </el-col>
        </el-row>
        <div class="success-rate" style="margin-top: 20px; text-align: center;">
          <span>成功率: {{ statistics.successRate ? statistics.successRate.toFixed(1) : 0 }}%</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDeviceDetectionData,
  deleteDeviceDetectionData,
  deleteBatchDeviceDetectionData,
  reparseExcelFile,
  getParseStatusStatistics,
  exportDetectionData
} from '@/api/analysis/deviceDetectionData'
import ltPagination from '@/components/lt-pagination/lt-pagination.vue'

export default {
  name: 'DetectionDataList',
  components: {
    ltPagination
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据列表
      dataList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        deviceName: null,
        templateName: null,
        parseStatus: null,
        startTime: null,
        endTime: null
      },
      // 显示统计信息
      showStatistics: false,
      // 显示滚动提示
      showScrollTip: true,
      // 统计数据
      statistics: {
        total: 0,
        success: 0,
        pending: 0,
        failed: 0,
        successRate: 0
      }
    }
  },
  created() {
    this.getList()
    this.getStatistics()
  },
  methods: {
    // 处理表格滚动
    handleTableScroll(event) {
      // 用户滚动后隐藏提示
      if (event.target.scrollLeft > 0) {
        this.showScrollTip = false
      }
    },
    /** 查询数据列表 */
    getList() {
      this.loading = true
      listDeviceDetectionData(this.queryParams).then(response => {
        if (response.code === 200) {
          this.dataList = response.data.records
          this.total = response.data.total
        } else {
          this.$message.error(response.msg || '查询失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      getParseStatusStatistics().then(response => {
        if (response.code === 200) {
          this.statistics = response.data
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 日期范围变化 */
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.queryParams.startTime = value[0]
        this.queryParams.endTime = value[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 上传按钮操作 */
    handleUpload() {
      this.$router.push('/detection/data/upload')
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push(`/detection/data/detail/${row.id}`)
    },
    /** 重新解析按钮操作 */
    handleReparse(row) {
      this.$confirm(`确认要重新解析文件"${row.fileName}"吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reparseExcelFile(row.id).then(response => {
          if (response.code === 200) {
            this.$message.success('重新解析已开始')
            this.getList()
          } else {
            this.$message.error(response.msg || '重新解析失败')
          }
        })
      })
    },
    /** 导出按钮操作 */
    handleExport(row) {
      this.$confirm(`确认要导出文件"${row.fileName}"的解析结果吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        exportDetectionData(row.id, 'excel').then(response => {
          this.$message.success('导出成功')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id != null ? [row.id] : this.ids
      const fileNames = row.fileName != null ? [row.fileName] :
        this.dataList.filter(item => ids.includes(item.id)).map(item => item.fileName)

      this.$confirm(`是否确认删除文件"${fileNames.join(', ')}"的数据？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (ids.length === 1) {
          deleteDeviceDetectionData(ids[0]).then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.getList()
              this.getStatistics()
            } else {
              this.$message.error(response.msg || '删除失败')
            }
          })
        } else {
          deleteBatchDeviceDetectionData(ids).then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.getList()
              this.getStatistics()
            } else {
              this.$message.error(response.msg || '删除失败')
            }
          })
        }
      })
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(1) + units[index]
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status] || 'info'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '待解析',
        1: '解析成功',
        2: '解析失败'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.statistics-content {
  padding: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-number.success {
  color: #67C23A;
}

.stat-number.warning {
  color: #E6A23C;
}

.stat-number.danger {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.success-rate {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.app-container {
  padding: 20px;
  height: calc(100vh - 120px); /* 减去导航栏高度 */
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.table-container {
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
  margin-bottom: 20px;
  flex: 1;
  min-height: 0; /* 重要：允许flex子项收缩 */
  border: 1px solid #ebeef5;
  border-radius: 4px;
  position: relative;
}

.el-table {
  min-width: 1100px;
}

/* 确保表格滚动条始终可见 */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保表格在小屏幕上也能正常显示 */
@media (max-width: 1200px) {
  .table-container {
    overflow-x: scroll;
  }

  .app-container {
    padding: 10px;
  }
}

/* 修复分页组件的显示 */
::v-deep .lt-pagination {
  margin-top: 20px;
  flex-shrink: 0;
}

/* 确保搜索表单不会被挤压 */
.el-form {
  flex-shrink: 0;
  margin-bottom: 20px;
}

/* 操作按钮行 */
.mb8 {
  flex-shrink: 0;
  margin-bottom: 20px;
}
</style>
